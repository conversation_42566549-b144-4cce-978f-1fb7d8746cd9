{% extends 'base.html' %}

{% block title %}Student Dashboard - Pathshala{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="ms-3">
                            <h2 class="mb-1">Welcome back, {{ user.first_name|default:user.username }}!</h2>
                            <p class="mb-0">Continue your learning journey</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3 col-6 mb-3">
            <div class="card text-center">
                <div class="card-body">
                    <div class="stat-value text-primary">{{ total_enrolled }}</div>
                    <div class="stat-label">Enrolled Courses</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-6 mb-3">
            <div class="card text-center">
                <div class="card-body">
                    <div class="stat-value text-success">{{ completed_courses }}</div>
                    <div class="stat-label">Completed</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-6 mb-3">
            <div class="card text-center">
                <div class="card-body">
                    <div class="stat-value text-warning">{{ certificates_earned }}</div>
                    <div class="stat-label">Certificates</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-6 mb-3">
            <div class="card text-center">
                <div class="card-body">
                    <div class="stat-value text-info">{{ total_hours|floatformat:0 }}</div>
                    <div class="stat-label">Hours Learned</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Courses -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Continue Learning</h5>
                    <a href="" class="btn btn-outline-primary btn-sm">Browse More</a>
                </div>
                <div class="card-body">
                    {% if enrollments %}
                        <div class="row">
                            {% for enrollment in enrollments %}
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card h-100">
                                        {% if enrollment.course_id.banner %}
                                            <img src="{{ enrollment.course_id.banner.url }}" class="card-img-top" style="height: 150px; object-fit: cover;" alt="{{ enrollment.course_id.title }}">
                                        {% else %}
                                            <img src="https://images.unsplash.com/photo-1517694712202-14dd9538aa97?ixlib=rb-1.2.1&auto=format&fit=crop&w=400&h=150&q=80" class="card-img-top" style="height: 150px; object-fit: cover;" alt="{{ enrollment.course_id.title }}">
                                        {% endif %}
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <a href="{% url 'course_detail' enrollment.course_id.id %}" class="text-decoration-none">{{ enrollment.course_id.title }}</a>
                                            </h6>
                                            <p class="card-text text-muted small">{{ enrollment.course_id.description }}</p>
                                            
                                            <!-- Progress Bar -->
                                            <div class="mb-2">
                                                <div class="d-flex justify-content-between mb-1">
                                                    <small class="text-muted">Progress</small>
                                                    <small class="text-muted">{{ enrollment.progress }}%</small>
                                                </div>
                                                <!-- Progress bar removed -->
                                            </div>
                                            
                                            <div class="d-flex justify-content-between align-items-center">
                                                {% if enrollment.is_completed %}
                                                    <span class="badge bg-success">Completed</span>
                                                    <a href="{% url 'course_detail' enrollment.course_id.id %}" class="btn btn-outline-primary btn-sm">Review</a>
                                                {% else %}
                                                    <span class="badge bg-warning">In Progress</span>
                                                    <a href="{% url 'course_detail' enrollment.course_id.id %}" class="btn btn-primary btn-sm">Continue</a>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-book-open fa-4x text-muted mb-3"></i>
                            <h5>No courses yet</h5>
                            <p class="text-muted">Start your learning journey by enrolling in a course</p>
                            <a href="" class="btn btn-primary">Browse Courses</a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Learning Activities -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Recent Activity</h5>
                </div>
                <div class="card-body">
                    <div class="activity-feed">
                        {% for enrollment in enrollments|slice:":5" %}
                            <div class="activity-item d-flex align-items-center mb-3">
                                <div class="activity-icon me-3">
                                    {% if enrollment.is_completed %}
                                        <i class="fas fa-check-circle text-success"></i>
                                    {% else %}
                                        <i class="fas fa-play-circle text-primary"></i>
                                    {% endif %}
                                </div>
                                <div>
                                    <div class="activity-title">
                                        {% if enrollment.is_completed %}
                                            Completed
                                        {% else %}
                                            Continue learning
                                        {% endif %}
                                        <a href="{% url 'course_detail' enrollment.course_id.id %}" class="text-decoration-none">{{ enrollment.course_id.title }}</a>
                                    </div>
                                    <div class="activity-time text-muted small">{{ enrollment.updated_at }} ago</div>
                                </div>
                            </div>
                        {% empty %}
                            <div class="text-center py-3">
                                <i class="fas fa-clock fa-2x text-muted mb-2"></i>
                                <p class="text-muted">No recent activity</p>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Achievements</h5>
                </div>
                <div class="card-body">
                    <div class="achievements-grid">
                        {% if completed_courses > 0 %}
                            <div class="achievement-item d-flex align-items-center mb-3">
                                <div class="achievement-icon me-3">
                                    <i class="fas fa-trophy text-warning"></i>
                                </div>
                                <div>
                                    <div class="achievement-title">Course Completionist</div>
                                    <div class="achievement-desc text-muted small">Completed {{ completed_courses }} course{{ completed_courses }}</div>
                                </div>
                            </div>
                        {% endif %}
                        
                        {% if certificates_earned > 0 %}
                            <div class="achievement-item d-flex align-items-center mb-3">
                                <div class="achievement-icon me-3">
                                    <i class="fas fa-certificate text-success"></i>
                                </div>
                                <div>
                                    <div class="achievement-title">Certified Learner</div>
                                    <div class="achievement-desc text-muted small">Earned {{ certificates_earned }} certificate{{ certificates_earned }}</div>
                                </div>
                            </div>
                        {% endif %}
                        
                        {% if total_enrolled > 2 %}
                            <div class="achievement-item d-flex align-items-center mb-3">
                                <div class="achievement-icon me-3">
                                    <i class="fas fa-star text-info"></i>
                                </div>
                                <div>
                                    <div class="achievement-title">Dedicated Learner</div>
                                    <div class="achievement-desc text-muted small">Enrolled in {{ total_enrolled }} courses</div>
                                </div>
                            </div>
                        {% endif %}
                        
                        {% if not completed_courses and not certificates_earned and total_enrolled < 3 %}
                            <div class="text-center py-3">
                                <i class="fas fa-medal fa-2x text-muted mb-2"></i>
                                <p class="text-muted">Complete courses to earn achievements</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.stat-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--dark);
}

.activity-icon {
    font-size: 1.2rem;
}

.activity-title {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.achievement-icon {
    font-size: 1.5rem;
    width: 40px;
    text-align: center;
}

.achievement-title {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}
</style>
{% endblock %}