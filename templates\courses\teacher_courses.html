{% extends 'base.html' %}
{% load course_extras %}

{% block title %}My Courses - Pathshala{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1>My Courses</h1>
            <p class="text-muted">Manage your courses and track student progress</p>
        </div>
        <div>
            <a href="{% url 'teacher_create_course' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Create New Course
            </a>
        </div>
    </div>

    <!-- Course Stats -->
    <div class="row mb-4">
        <div class="col-md-3 col-6 mb-3">
            <div class="card text-center">
                <div class="card-body">
                    <div class="stat-value text-primary">{{ courses.count }}</div>
                    <div class="stat-label">Total Courses</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-6 mb-3">
            <div class="card text-center">
                <div class="card-body">
                    <div class="stat-value text-success">{{ courses|length }}</div>
                    <div class="stat-label">Published</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-6 mb-3">
            <div class="card text-center">
                <div class="card-body">
                    <div class="stat-value text-warning">0</div>
                    <div class="stat-label">Draft</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-6 mb-3">
            <div class="card text-center">
                <div class="card-body">
                    <div class="stat-value text-info">
                        {% for course in courses %}
                            {% for enrollment in course.enrollment_set.all %}
                                {{ forloop.counter }}
                            {% endfor %}
                        {% endfor %}
                    </div>
                    <div class="stat-label">Students</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Courses Grid -->
    <div class="row">
        {% for course in courses %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card course-card h-100">
                    <div class="position-relative">
                        {% if course.banner %}
                            <img src="{{ course.banner.url }}" class="card-img-top" alt="{{ course.title }}">
                        {% else %}
                            <img src="https://images.unsplash.com/photo-1517694712202-14dd9538aa97?ixlib=rb-1.2.1&auto=format&fit=crop&w=400&h=240&q=80" class="card-img-top" alt="{{ course.title }}">
                        {% endif %}
                        <span class="course-status {% if course.is_active %}published{% else %}draft{% endif %}">
                            {% if course.is_active %}PUBLISHED{% else %}DRAFT{% endif %}
                        </span>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="badge badge-category">{{ course.category_id.title }}</span>
                            <div class="rating">
                                {% for i in "12345"|make_list %}
                                    <i class="fas fa-star"></i>
                                {% endfor %}
                                <span class="ms-1 text-dark">{{ course|course_rating }}</span>
                            </div>
                        </div>
                        <h5 class="card-title">{{ course.title }}</h5>
                        <p class="card-text">{{ course.description|truncate_words:15 }}</p>
                        
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-users text-primary me-2"></i>
                            <span>{{ course.enrollment_set.count }} enrolled</span>
                        </div>
                        
                        <div class="mb-3">
                            <div class="d-flex justify-content-between mb-1">
                                <small>Course completion rate</small>
                                <small>78%</small>
                            </div>
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 78%;" aria-valuenow="78" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'course_detail' course.id %}" class="btn btn-primary">View Course</a>
                            <div class="btn-group">
                                <a href="{% url 'teacher_edit_course' course.id %}" class="btn btn-outline-secondary btn-sm" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'teacher_course_students' course.id %}" class="btn btn-outline-info btn-sm" title="Students">
                                    <i class="fas fa-users"></i>
                                </a>
                                <a href="{% url 'teacher_course_lessons' course.id %}" class="btn btn-outline-success btn-sm" title="Lessons">
                                    <i class="fas fa-play"></i>
                                </a>
                                <a href="{% url 'teacher_course_materials' course.id %}" class="btn btn-outline-warning btn-sm" title="Materials">
                                    <i class="fas fa-file-alt"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">Last updated: {{ course.updated_at|timesince }} ago</small>
                            <span class="badge bg-primary">{{ course.price|format_price }}</span>
                        </div>
                    </div>
                </div>
            </div>
        {% empty %}
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-chalkboard-teacher fa-4x text-muted mb-3"></i>
                    <h3>No courses yet</h3>
                    <p class="text-muted">Create your first course to start teaching and sharing your knowledge.</p>
                    <a href="{% url 'teacher_create_course' %}" class="btn btn-primary btn-lg">Create Your First Course</a>
                </div>
            </div>
        {% endfor %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.course-status {
    position: absolute;
    top: 15px;
    left: 15px;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    color: white;
}

.course-status.published {
    background-color: var(--success);
}

.course-status.draft {
    background-color: var(--warning);
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--dark);
}

.btn-group .btn {
    padding: 0.25rem 0.5rem;
}

.card-footer {
    border-top: 1px solid rgba(0,0,0,0.1);
}
</style>
{% endblock %}