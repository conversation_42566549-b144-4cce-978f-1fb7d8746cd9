{% extends 'base.html' %}
{% load course_extras %}

{% block title %}Courses - Pathshala{% endblock %}

{% block content %}
<!-- Page Header -->
<header class="page-header">
    <div class="container">
        <h1>Browse Courses</h1>
        <p class="lead">Discover thousands of courses to start learning something new</p>
    </div>
</header>

<!-- Main Content -->
<div class="container">
    <div class="row">
        <!-- Filters Sidebar -->
        <div class="col-lg-3">
            <div class="filters-sidebar">
                <!-- Categories Filter -->
                <div class="card filter-card">
                    <div class="card-header">
                        <h5 class="mb-0">Categories</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="category" id="categoryAll" value="" 
                                   {% if not category_filter %}checked{% endif %}>
                            <label class="form-check-label" for="categoryAll">
                                All Categories
                            </label>
                        </div>
                        {% for category in categories %}
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="category" 
                                       id="category{{ category.id }}" value="{{ category.id }}"
                                       {% if category_filter == category.id|stringformat:"s" %}checked{% endif %}>
                                <label class="form-check-label" for="category{{ category.id }}">
                                    {{ category.title }}
                                </label>
                            </div>
                        {% endfor %}
                    </div>
                </div>
                
                <!-- Price Filter -->
                <div class="card filter-card">
                    <div class="card-header">
                        <h5 class="mb-0">Price</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="price" id="priceAll" value=""
                                   {% if not price_filter %}checked{% endif %}>
                            <label class="form-check-label" for="priceAll">
                                All Prices
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="price" id="priceFree" value="free"
                                   {% if price_filter == 'free' %}checked{% endif %}>
                            <label class="form-check-label" for="priceFree">
                                Free
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="price" id="pricePaid" value="paid"
                                   {% if price_filter == 'paid' %}checked{% endif %}>
                            <label class="form-check-label" for="pricePaid">
                                Paid
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- Apply Filters Button -->
                <button class="btn btn-primary w-100 mb-4" id="applyFilters">Apply Filters</button>
            </div>
        </div>
        
        <!-- Courses Grid -->
        <div class="col-lg-9">
            <!-- Search and Sort Options -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <p class="mb-0">
                        Showing <strong>{{ courses.start_index }}-{{ courses.end_index }}</strong> 
                        of <strong>{{ courses.paginator.count }}</strong> courses
                        {% if search %}for "{{ search }}"{% endif %}
                    </p>
                </div>
                <div class="d-flex align-items-center">
                    <label class="me-2">Sort by:</label>
                    <select class="form-select" id="sortSelect" style="width: auto;">
                        <option value="popular" {% if sort_by == 'popular' %}selected{% endif %}>Most Popular</option>
                        <option value="newest" {% if sort_by == 'newest' %}selected{% endif %}>Newest</option>
                        <option value="price_low" {% if sort_by == 'price_low' %}selected{% endif %}>Price: Low to High</option>
                        <option value="price_high" {% if sort_by == 'price_high' %}selected{% endif %}>Price: High to Low</option>
                    </select>
                </div>
            </div>
            
            <!-- Courses Row -->
            <div class="row">
                {% for course in courses %}
                    <div class="col-md-6 col-lg-4">
                        {% course_card course user %}
                    </div>
                {% empty %}
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="fas fa-search fa-4x text-muted mb-3"></i>
                            <h3>No courses found</h3>
                            <p class="text-muted">Try adjusting your search criteria or browse all courses.</p>
                            <a href="{% url 'courses_list' %}" class="btn btn-primary">Browse All Courses</a>
                        </div>
                    </div>
                {% endfor %}
            </div>
            
            <!-- Pagination -->
            {% if courses.has_other_pages %}
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if courses.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?{% url_replace page=courses.previous_page_number %}">Previous</a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">Previous</span>
                            </li>
                        {% endif %}
                        
                        {% for num in courses.paginator.page_range %}
                            {% if courses.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > courses.number|add:'-3' and num < courses.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?{% url_replace page=num %}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if courses.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?{% url_replace page=courses.next_page_number %}">Next</a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">Next</span>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle filter application
    document.getElementById('applyFilters').addEventListener('click', function() {
        const form = document.createElement('form');
        form.method = 'GET';
        form.action = '{% url "courses_list" %}';
        
        // Add search term if exists
        {% if search %}
            const searchInput = document.createElement('input');
            searchInput.type = 'hidden';
            searchInput.name = 'search';
            searchInput.value = '{{ search }}';
            form.appendChild(searchInput);
        {% endif %}
        
        // Add category filter
        const categoryFilter = document.querySelector('input[name="category"]:checked');
        if (categoryFilter && categoryFilter.value) {
            const categoryInput = document.createElement('input');
            categoryInput.type = 'hidden';
            categoryInput.name = 'category';
            categoryInput.value = categoryFilter.value;
            form.appendChild(categoryInput);
        }
        
        // Add price filter
        const priceFilter = document.querySelector('input[name="price"]:checked');
        if (priceFilter && priceFilter.value) {
            const priceInput = document.createElement('input');
            priceInput.type = 'hidden';
            priceInput.name = 'price';
            priceInput.value = priceFilter.value;
            form.appendChild(priceInput);
        }
        
        // Add sort option
        const sortSelect = document.getElementById('sortSelect');
        if (sortSelect.value) {
            const sortInput = document.createElement('input');
            sortInput.type = 'hidden';
            sortInput.name = 'sort';
            sortInput.value = sortSelect.value;
            form.appendChild(sortInput);
        }
        
        document.body.appendChild(form);
        form.submit();
    });
    
    // Handle sort change
    document.getElementById('sortSelect').addEventListener('change', function() {
        document.getElementById('applyFilters').click();
    });
});
</script>
{% endblock %}

{% block extra_css %}
<style>
.page-header {
    background: linear-gradient(135deg, var(--primary) 0%, #8c98f3 100%);
    color: white;
    padding: 3rem 0;
    border-radius: 0 0 20px 20px;
    margin-bottom: 3rem;
}

.filter-card {
    border-radius: 0.75rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    border: none;
    margin-bottom: 1.5rem;
}

.filter-card .card-header {
    background-color: white;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    font-weight: 600;
    padding: 1rem 1.25rem;
    border-radius: 0.75rem 0.75rem 0 0;
}

.filter-card .form-check {
    margin-bottom: 0.5rem;
}

.filters-sidebar {
    position: sticky;
    top: 90px;
}

@media (max-width: 991.98px) {
    .filters-sidebar {
        position: static;
        margin-bottom: 2rem;
    }
}
</style>
{% endblock %}