{% load course_extras %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Pathshala - Learning Management System{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary: #5e72e4;
            --secondary: #f7fafc;
            --success: #2dce89;
            --info: #11cdef;
            --warning: #fb6340;
            --danger: #f5365c;
            --light: #f8f9fe;
            --dark: #172b4d;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--dark);
            background-color: var(--light);
                        {% if not request.resolver_match.url_name == 'home' %}padding-top: 76px;{% endif %}

        }
        
        .navbar {
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary);
        }
        
        .nav-link {
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        
        .btn-primary:hover {
            background-color: #4863db;
            border-color: #4863db;
        }
        
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        
        .btn-outline-primary:hover {
            background-color: var(--primary);
            border-color: var(--primary);
            color: white;
        }
        
        .card {
            border: none;
            border-radius: 0.75rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .course-card .card-img-top {
            height: 160px;
            object-fit: cover;
            border-radius: 0.75rem 0.75rem 0 0;
        }
        
        .badge-category {
            background-color: rgba(94, 114, 228, 0.1);
            color: var(--primary);
            font-weight: 500;
        }
        
        .progress {
            height: 8px;
            border-radius: 4px;
        }
        
        .progress-bar {
            background-color: var(--primary);
        }
        
        .rating {
            color: #ffc107;
        }
        
        .footer {
            background-color: var(--dark);
            color: white;
            padding: 4rem 0 2rem;
            margin-top: 4rem;
        }
        
        .footer a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
        }
        
        .footer a:hover {
            color: white;
        }
        
        .footer-heading {
            font-weight: 600;
            font-size: 1.2rem;
            margin-bottom: 1.5rem;
            color: white;
        }
        
        .user-avatar {
            background: linear-gradient(45deg, var(--primary), #8c98f3);
        }
        
        .pagination .page-item.active .page-link {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        
        .pagination .page-link {
            color: var(--primary);
        }
        
        /* Custom message styles */
        .alert {
            border-radius: 0.75rem;
            border: none;
        }
        
        
        .navbar {
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary);
        }
        
        .nav-link {
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        
        .btn-primary:hover {
            background-color: #4863db;
            border-color: #4863db;
        }
        
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        
        .btn-outline-primary:hover {
            background-color: var(--primary);
            border-color: var(--primary);
            color: white;
        }
        
        .hero {
            background: linear-gradient(135deg, var(--primary) 0%, #8c98f3 100%);
            color: white;
            padding: 6rem 0;
            border-radius: 0 0 20px 20px;
        }
        
        .hero h1 {
            font-weight: 700;
            font-size: 3rem;
        }
        
        .card {
            border: none;
            border-radius: 0.75rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .course-card .card-img-top {
            height: 160px;
            object-fit: cover;
            border-radius: 0.75rem 0.75rem 0 0;
        }
        
        .course-instructor {
            display: flex;
            align-items: center;
        }
        
        .instructor-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 10px;
        }
        
        .role-card {
            padding: 2rem;
            text-align: center;
            height: 100%;
        }
        
        .role-icon {
            font-size: 3rem;
            margin-bottom: 1.5rem;
            color: var(--primary);
        }
        
        .role-card h3 {
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .stats-section {
            background-color: var(--secondary);
            padding: 5rem 0;
            border-radius: 20px;
            margin: 4rem 0;
        }
        
        .stat-value {
            font-size: 3rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            font-size: 1.1rem;
            color: var(--dark);
            font-weight: 500;
        }
        
        .footer {
            background-color: var(--dark);
            color: white;
            padding: 4rem 0 2rem;
        }
        
        .footer a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
        }
        
        .footer a:hover {
            color: white;
        }
        
        .footer-heading {
            font-weight: 600;
            font-size: 1.2rem;
            margin-bottom: 1.5rem;
            color: white;
        }
        
        .badge-category {
            background-color: rgba(94, 114, 228, 0.1);
            color: var(--primary);
            font-weight: 500;
        }
        
        .progress {
            height: 8px;
            border-radius: 4px;
        }
        
        .rating {
            color: #ffc107;
        }
        
        .category-pill {
            border-radius: 20px;
            padding: 0.35rem 1rem;
            margin: 0.25rem;
            display: inline-block;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .category-pill:hover, .category-pill.active {
            background-color: var(--primary);
            color: white;
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background-color: rgba(94, 114, 228, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
            font-size: 2rem;
            color: var(--primary);
        }
        
        .testimonial-card {
            border-radius: 0.75rem;
            overflow: hidden;
        }
        
        .testimonial-img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .search-form {
            background-color: white;
            border-radius: 50px;
            padding: 0.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .search-form .form-control {
            border: none;
            padding-left: 1rem;
        }
        
        .search-form .form-control:focus {
            box-shadow: none;
        }
        
        .search-form .btn {
            border-radius: 50px;
            padding-left: 1.5rem;
            padding-right: 1.5rem;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white {% if not request.resolver_match.url_name == 'home' %}fixed-top{% else %}sticky-top{% endif %}">
        <div class="container">
            <a class="navbar-brand" href="{% url 'home' %}">
                <i class="fas fa-graduation-cap me-2"></i>Pathshala
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'home' %}active{% endif %}" href="{% url 'home' %}">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if 'courses' in request.resolver_match.url_name %}active{% endif %}" href="{% url 'courses_list' %}">Courses</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">Teachers</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">Contact</a>
                    </li>
                </ul>
                
                <form class="d-flex me-3" action="{% url 'courses_list' %}" method="GET">
                    <div class="input-group">
                        <input class="form-control" type="search" name="search" placeholder="Search courses..." value="{{ search|default:'' }}">
                        <button class="btn btn-outline-primary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
                
                <div>
                    {% if user.is_authenticated %}
                        <div class="dropdown">
                            <a href="#" class="d-flex align-items-center text-decoration-none dropdown-toggle" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                {% user_avatar user 32 %}
                                <span class="ms-2">{{ user.get_full_name|default:user.username }}</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="{% url 'dashboard' %}">
                                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                </a></li>
                                
                                {% if user.role == 'student' %}
                                    <li><a class="dropdown-item" href="{% url 'courses_list' %}?enrolled=true">
                                        <i class="fas fa-book me-2"></i>My Courses
                                    </a></li>
                                {% elif user.role == 'teacher' %}
                                    <li><a class="dropdown-item" href="{% url 'teacher_courses' %}">
                                        <i class="fas fa-chalkboard me-2"></i>My Courses
                                    </a></li>
                                    <li><a class="dropdown-item" href="{% url 'teacher_create_course' %}">
                                        <i class="fas fa-plus me-2"></i>Create Course
                                    </a></li>
                                
                                {% endif %}
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'logout' %}">
                                    <i class="fas fa-sign-out-alt me-2"></i>Log Out
                                </a></li>
                            </ul>
                        </div>
                    {% else %}
                        <a href="{% url 'login' %}" class="btn btn-outline-primary me-2">Login</a>
                        <a href="{% url 'signup' %}" class="btn btn-primary">Sign Up</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <!-- Messages -->
    {% if messages %}
        <div class="container mt-3">
            {% for message in messages %}
                <div class="alert {{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Main Content -->
    {% block content %}{% endblock %}

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-4">
                    <h5 class="footer-heading">Pathshala</h5>
                    <p>A comprehensive learning platform to help you achieve your personal and professional goals.</p>
                    <div class="mt-3">
                        <a href="#" class="me-3"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="me-3"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <h5 class="footer-heading">Courses</h5>
                    <ul class="list-unstyled">
                        {% for category in categories|slice:":5" %}
                            <li class="mb-2"><a href="{% url 'courses_list' %}?category={{ category.id }}">{{ category.title }}</a></li>
                        {% endfor %}
                    </ul>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <h5 class="footer-heading">Quick Links</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="#">About Us</a></li>
                        <li class="mb-2"><a href="#">Contact</a></li>
                        <li class="mb-2"><a href="#">Careers</a></li>
                        <li class="mb-2"><a href="{% url 'signup' %}?role=teacher">Become a Teacher</a></li>
                        <li class="mb-2"><a href="#">Blog</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <h5 class="footer-heading">Contact Us</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-map-marker-alt me-2"></i>123 Education St, Learning City</li>
                        <li class="mb-2"><i class="fas fa-phone me-2"></i>+****************</li>
                        <li class="mb-2"><i class="fas fa-envelope me-2"></i><EMAIL></li>
                    </ul>
                    
                    <h5 class="footer-heading mt-4">Subscribe to Newsletter</h5>
                    <form>
                        <div class="input-group">
                            <input type="email" class="form-control" placeholder="Your email">
                            <button class="btn btn-primary">Subscribe</button>
                        </div>
                    </form>
                </div>
            </div>
            <hr class="mt-4 mb-4" style="border-color: rgba(255, 255, 255, 0.1);">
            <div class="row">
                <div class="col-md-6 text-center text-md-start">
                    <p class="mb-0">&copy; 2025 Pathshala Learning Management System. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <a href="#" class="text-white me-3">Privacy Policy</a>
                    <a href="#" class="text-white me-3">Terms of Service</a>
                    <a href="#" class="text-white">Cookie Policy</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>