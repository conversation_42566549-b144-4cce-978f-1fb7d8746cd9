{% extends 'base.html' %}
{% load static %}
{% block title %}Create Material - {{ course.title }} | Pathshala{% endblock %}

{% block content %}
<div class="container my-5">
    <h2 class="mb-4 text-primary">Add New Material for: {{ course.title }}</h2>

    <form method="POST" enctype="multipart/form-data" class="card p-4 shadow-sm">
        {% csrf_token %}
        
        <div class="mb-3">
            <label for="title" class="form-label fw-semibold">Material Title</label>
            <input type="text" name="title" id="title" class="form-control" placeholder="e.g. Week 1 Lecture Notes" required>
        </div>

        <div class="mb-3">
            <label for="description" class="form-label fw-semibold">Description</label>
            <textarea name="description" id="description" class="form-control" rows="4" placeholder="Brief overview of the material" required></textarea>
        </div>

        <div class="mb-3">
            <label for="file_type" class="form-label fw-semibold">File Type</label>
            <select name="file_type" id="file_type" class="form-select" required>
                <option value="">-- Select File Type --</option>
                <option value="pdf">PDF</option>
                <option value="docx">DOCX</option>
                <option value="ppt">PPT</option>
                <option value="image">Image</option>
                <option value="other">Other</option>
            </select>
        </div>

        <div class="mb-3">
            <label for="file" class="form-label fw-semibold">Upload File</label>
            <input type="file" name="file" id="file" class="form-control" required>
        </div>

        <div class="d-flex justify-content-between mt-4">
            <a href="{% url 'teacher_course_materials' course.id %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Materials
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-upload me-1"></i> Upload Material
            </button>
        </div>
    </form>
</div>
{% endblock %}
